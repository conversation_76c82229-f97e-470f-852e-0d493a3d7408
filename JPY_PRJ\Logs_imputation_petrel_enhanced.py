# # Missing Logs Imputation – **Petrel‑connected** workflow with Interactive Selection
# 
# This notebook connects directly to the **currently open Petrel project** with Cegal Prizm Python Tool Pro, provides interactive dropdown menus for well and log selection, pulls the selected global well logs for every well, applies machine‑learning models to fill in missing values, and (optionally) writes the imputed logs back into Petrel.
# 
# *Enhanced with interactive well and log selection - Generated 2025-06-19*.

# Core libraries
import numpy as np
import pandas as pd
import warnings
warnings.filterwarnings('ignore')

# ML libraries
from xgboost import XGBRegressor
from lightgbm import LGBMRegressor
from catboost import CatBoostRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error

# Plotting (optional)
import plotly.express as px

# Interactive widgets for log selection
import ipywidgets as widgets
from IPython.display import display, clear_output

# Petrel connection
from cegalprizm.pythontool import PetrelConnection

petrel = PetrelConnection()
print(f'Connected to Petrel project: {petrel.get_current_project_name()}')


# ## 1 – Interactive Well and Log Selection

# Create interactive dropdowns for well and log selection
print('Setting up interactive selection interface...')

# Get all available global well logs
available_logs = {}
for log in petrel.global_well_logs:
    if hasattr(log, 'petrel_name'):
        available_logs[log.petrel_name] = log
    elif isinstance(log, list):
        for sub_log in log:
            if hasattr(sub_log, 'petrel_name'):
                available_logs[sub_log.petrel_name] = sub_log

log_names = sorted(available_logs.keys())
print(f'Found {len(log_names)} global well logs')

# Create log selection widgets
input_logs_widget = widgets.SelectMultiple(
    options=log_names,
    value=tuple(['GR', 'RHOB', 'NPHI', 'Vp']) if all(log in log_names for log in ['GR', 'RHOB', 'NPHI', 'Vp']) else tuple(log_names[:4]) if len(log_names) >= 4 else tuple(log_names),
    description='Input Logs:',
    disabled=False,
    layout=widgets.Layout(width='400px', height='120px')
)

target_log_widget = widgets.Dropdown(
    options=log_names,
    value='Vs' if 'Vs' in log_names else log_names[0] if log_names else None,
    description='Target Log:',
    disabled=False,
    layout=widgets.Layout(width='400px')
)

# Get all wells and create well selection widget
print('Scanning available wells...')
wells = [w for w in petrel.wells]\n,
well_names = [w.petrel_name for w in wells]\n,
print(f'Found {len(wells)} wells')

# Create well selection widget
wells_widget = widgets.SelectMultiple(
    options=well_names,
    value=tuple(well_names[:min(5, len(well_names))]),  # Select first 5 wells by default
    description='Wells:',
    disabled=False,
    layout=widgets.Layout(width='400px', height='120px')
)

# Display widgets
print('\nSelect your wells and logs:')
display(widgets.VBox([
    widgets.HTML('<b>Select Wells (hold Ctrl/Cmd for multiple selection):</b>'),
    wells_widget,
    widgets.HTML('<br><b>Select Input Logs (hold Ctrl/Cmd for multiple selection):</b>'),
    input_logs_widget,
    widgets.HTML('<br><b>Select Target Log for Imputation:</b>'),
    target_log_widget
]))

# Store widgets for later use
log_selection_widgets = {
    'wells': wells_widget,
    'input_logs': input_logs_widget,
    'target_log': target_log_widget,
    'available_logs': available_logs,
    'all_wells': wells
}


ptp = PetrelConnection()

print(f'Currently open Petrel project is {ptp.get_current_project_name()}')


ptp = PetrelConnection()

print(f'Currently open Petrel project is {ptp.get_current_project_name()}')


# ## 2 – Configure wells & apply selection

# Get selected wells from widget
selected_well_names = list(wells_widget.value)
selected_wells = [w for w in wells if w.petrel_name in selected_well_names]
print(f'Selected {len(selected_wells)} wells: {[w.petrel_name for w in selected_wells]}')

# Get selected logs from widgets
selected_input_logs = list(input_logs_widget.value)
selected_target_log = target_log_widget.value

print(f'Selected input logs: {selected_input_logs}')
print(f'Selected target log: {selected_target_log}')

# Combine all log names
LOG_NAMES = selected_input_logs + [selected_target_log]
print(f'All logs to process: {LOG_NAMES}')

# Helper function to find global well logs by name
def find_global_well_logs_by_names(names):
    found_logs = []
    for name in names:
        if name in available_logs:
            found_logs.append(available_logs[name])
        else:
            print(f'Warning: Log {name} not found in available logs')
    return found_logs

logs = find_global_well_logs_by_names(LOG_NAMES)
print(f'Using {len(logs)} global logs: {[g.petrel_name for g in logs]}')

# Validate that we have both wells and logs selected
if not selected_wells:
    print('ERROR: No wells selected! Please select wells in the dropdown above.')
if not logs:
    print('ERROR: No valid logs found! Please check your log selection.')
if selected_wells and logs:
    print('✓ Ready to load data from selected wells and logs')


# ## 3 – Load log data from Petrel (Fixed with Error Handling)

# Ensure we have the required variables from previous cells
try:
    # Check if variables exist from section 2
    if 'selected_wells' not in locals():
        print('Getting wells and logs from widgets...')
        selected_well_names = list(wells_widget.value)
        selected_wells = [w for w in wells if w.petrel_name in selected_well_names]
        
    if 'logs' not in locals():
        selected_input_logs = list(input_logs_widget.value)
        selected_target_log = target_log_widget.value
        LOG_NAMES = selected_input_logs + [selected_target_log]
        
        def find_global_well_logs_by_names(names):
            found_logs = []
            for name in names:
                if name in available_logs:
                    found_logs.append(available_logs[name])
            return found_logs
        
        logs = find_global_well_logs_by_names(LOG_NAMES)
        
    print(f'Loading data from {len(selected_wells)} wells with {len(logs)} logs...')
    
    # Initialize empty DataFrame
    well_data = pd.DataFrame()
    
    # Load data from each selected well
    for i, w in enumerate(selected_wells):
        print(f'Processing well {i+1}/{len(selected_wells)}: {w.petrel_name}')
        try:
            df = w.logs_dataframe(logs)      # returns MD‑indexed DataFrame
            if not df.empty:
                df['WELL'] = w.petrel_name
                well_data = pd.concat([well_data, df], ignore_index=False)
                print(f'  ✓ Loaded {len(df)} samples')
            else:
                print(f'  ⚠ No data found for this well')
        except Exception as e:
            print(f'  ✗ Error loading data: {str(e)}')
    
    # Reset index to make MD a column
    if not well_data.empty:
        well_data.reset_index(drop=False, inplace=True)  # MD becomes column
        print(f'\n✓ Combined DataFrame shape: {well_data.shape}')
        print(f'Columns: {list(well_data.columns)}')
        display(well_data.head())
    else:
        print('\n✗ No data loaded! Check your well and log selections.')
        
except Exception as e:
    print(f'Error in data loading: {str(e)}')
    print('Make sure you have run the previous cells to select wells and logs.')


# ## 4 – Basic cleaning (remove obvious spikes)

# Clean GR if it's in the selected logs
if 'GR' in well_data.columns:
    GR_MAX = 300
    well_data['GR'] = np.where(well_data['GR'] <= GR_MAX, well_data['GR'], np.nan)
    print(f'Cleaned GR values > {GR_MAX}')
    
# Clean NPHI if it's in the selected logs
if 'NPHI' in well_data.columns:
    well_data['NPHI'] = np.where(
        (well_data['NPHI'] >= 0) & (well_data['NPHI'] <= 1),
        well_data['NPHI'], np.nan
    )
    print('Cleaned NPHI values outside [0, 1] range')
    
# Clean RHOB if it's in the selected logs
if 'RHOB' in well_data.columns:
    well_data['RHOB'] = np.where(
        (well_data['RHOB'] >= 1.0) & (well_data['RHOB'] <= 3.5),
        well_data['RHOB'], np.nan
    )
    print('Cleaned RHOB values outside [1.0, 3.5] range')
    
print('\nData summary after cleaning:')
display(well_data.describe().T)


# ## 5 – Coverage per log

# Calculate coverage for selected logs only
log_columns = [col for col in LOG_NAMES if col in well_data.columns]
coverage = 1.0 - well_data[log_columns].isna().mean()
print('Data coverage per log:')
for log_name, cov in coverage.items():
    print(f'  {log_name}: {cov:.2%}')

# Visualize coverage
fig = px.bar(coverage, labels={'value':'Coverage'}, title='Relative data coverage for selected logs')
fig.show()


# ## 6 – Imputation utility with enhanced ML models

def impute_logs(df, depth_col, feature_cols, targets):
    """Return DataFrame with *_pred, *_imputed, *_error columns."""
    res = df.copy()
    # Configure ML models with user's preferred hyperparameters
    boosters = [
        ('EXTREME BOOST REGRESSOR', XGBRegressor(
            n_estimators=300,
            tree_method='gpu_hist',
            learning_rate=0.05,
            early_stopping_rounds=100,
            random_state=42
        )),
        ('LGBM REGRESSOR', LGBMRegressor(
            device='gpu',
            gpu_platform_id=1,
            gpu_device_id=0,
            n_estimators=300,
            random_state=42
        )),
        ('CATBOOST REGRESSOR', CatBoostRegressor(
            task_type='GPU',
            early_stopping_rounds=100,
            verbose=0,
            random_state=42
        ))
    ]
    feature_set = feature_cols + [depth_col]

    for tgt in targets:
        print(f'--- Imputing {tgt} ---')
        train = res[res[tgt].notna()][feature_set + [tgt]].copy()
        if train.empty:
            print('No training data, skipping.')
            continue
        X = train.drop(columns=[tgt]).apply(lambda c: c.fillna(c.mean()), axis=0)
        y = train[tgt]

        Xtr, Xval, ytr, yval = train_test_split(X, y, test_size=0.25, random_state=42)
        best_model, best_name, best_mae = None, None, float("inf")
        
        print(f'Training {len(boosters)} models on {len(Xtr)} samples...')
        for name, model in boosters:
            try:
                model.fit(Xtr, ytr)
                mae = mean_absolute_error(yval, model.predict(Xval))
                print(f'  {name}: MAE = {mae:.3f}')
                if mae < best_mae:
                    best_model, best_name, best_mae = model, name, mae
            except Exception as e:
                print(f'  {name}: Failed - {str(e)}')
                
        if best_model is None:
            print('All models failed! Skipping this target.')
            continue
            
        print(f'✓ Best model: {best_name} (MAE={best_mae:.3f})')

        X_full = res[feature_set].apply(lambda c: c.fillna(c.mean()), axis=0)
        preds = best_model.predict(X_full)
        res[f'{tgt}_pred'] = preds
        res[f'{tgt}_imputed'] = res[tgt].fillna(preds)
        res[f'{tgt}_error'] = np.abs(res[tgt] - preds) / res[tgt] * 100
        
        # Show imputation statistics
        missing_count = res[tgt].isna().sum()
        total_count = len(res)
        print(f'Imputed {missing_count}/{total_count} missing values ({missing_count/total_count:.1%})')
        
    return res


# ## 7 – Run imputation with selected logs

DEPTH_COL = 'MD'
FEATURES = selected_input_logs  # Use selected input logs
TARGET = selected_target_log    # Use selected target log

print(f'Running imputation for target: {TARGET}')
print(f'Using features: {FEATURES}')
print(f'Depth column: {DEPTH_COL}')

# Run the imputation
results = impute_logs(well_data, DEPTH_COL, FEATURES, targets=[TARGET])

print(f'\nImputation completed! Results shape: {results.shape}')
print(f'New columns added: {[col for col in results.columns if col.endswith("_pred") or col.endswith("_imputed") or col.endswith("_error")]}')

display(results.head())


# ## 8 – (Option) Write imputed logs back to Petrel

def write_back_to_petrel(results_df, log_name_in_results, clone_from=None):
    """Clone an existing log (or first available log) and overwrite with imputed values."""
    # Ensure we have the selected wells
    if 'selected_wells' not in locals():
        selected_well_names = list(wells_widget.value)
        selected_wells = [w for w in wells if w.petrel_name in selected_well_names]
    
    if clone_from is None:
        clone_from = target_log_widget.value  # Use the selected target log as template
        
    print(f'Writing back {log_name_in_results} to {len(selected_wells)} wells...')
    success_count = 0
    
    for w in selected_wells:
        print(f'Updating {w.petrel_name}')
        well_df = results_df[results_df['WELL'] == w.petrel_name].set_index('MD')
        if well_df.empty:
            print(f'  ⚠ No data for well {w.petrel_name}, skipping')
            continue
            
        md = well_df.index.to_numpy()
        values = well_df[log_name_in_results].to_numpy()

        # Find a log to clone (or existing target)
        target_logs = [log for log in w.logs if log.petrel_name == log_name_in_results]
        if target_logs:
            log_obj = target_logs[0]
            print(f'  Using existing log {log_name_in_results}')
        else:
            # Clone template log
            template_logs = [log for log in w.logs if log.petrel_name == clone_from]
            if template_logs:
                template = template_logs[0]
                log_obj = template.clone(w, log_name_in_results)
                print(f'  Created new log {log_name_in_results} from template {clone_from}')
            else:
                print(f'  ✗ No template log {clone_from} found in well {w.petrel_name}, skipping')
                continue

        try:
            petrel_log_ref = petrel.well_logs[log_obj.path]
            petrel_log_ref.readonly = False
            petrel_log_ref.set_values(md, values)
            print(f'  ✓ Successfully updated {log_name_in_results} for {w.petrel_name}')
            success_count += 1
        except Exception as e:
            print(f'  ✗ Error updating {log_name_in_results} for {w.petrel_name}: {str(e)}')
    
    print(f'\nWrite-back completed: {success_count}/{len(selected_wells)} wells updated successfully.')

# Example usage - uncomment to execute
try:
    imputed_log_name = f'{target_log_widget.value}_imputed'
    print(f'To write back imputed values, run:')
    print(f'write_back_to_petrel(results, \'{imputed_log_name}\')')
    print('\nUncomment the line below to execute:')
    # write_back_to_petrel(results, imputed_log_name)
except:
    print('Run the log selection cells first to enable write-back functionality.')


