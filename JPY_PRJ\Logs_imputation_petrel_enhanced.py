# # Missing Logs Imputation – **Petrel‑connected** workflow with Interactive Selection
#
# This script connects directly to the **currently open Petrel project** with Cegal Prizm Python Tool Pro,
# provides console-based interactive menus for well and log selection, pulls the selected global well logs
# for every well, applies machine‑learning models to fill in missing values, and (optionally) writes the
# imputed logs back into Petrel.
#
# *Enhanced with console-based interactive well and log selection - Generated 2025-06-20*.
#
# USAGE:
# 1. Run this script in Petrel's Python environment (not Jupyter)
# 2. Follow the console prompts to select wells and logs
# 3. The script will automatically process the data and generate imputed logs
# 4. Optionally write results back to Petrel
#
# REQUIREMENTS:
# - Active Petrel project with well log data
# - Cegal Prizm Python Tool Pro
# - Required Python packages: numpy, pandas, xgboost, lightgbm, catboost, sklearn, plotly

# Core libraries
import numpy as np
import pandas as pd
import warnings
warnings.filterwarnings('ignore')

# ML libraries
from xgboost import XGBRegressor
from lightgbm import LGBMRegressor
from catboost import CatBoostRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error

# Plotting (optional)
import plotly.express as px

# Petrel connection
from cegalprizm.pythontool import PetrelConnection

petrel = PetrelConnection()
print(f'Connected to Petrel project: {petrel.get_current_project_name()}')


# ## Console-based Interactive Selection Functions

def console_select_multiple(options, prompt, default_selections=None, max_selections=None):
    """
    Console-based multiple selection function.

    Args:
        options: List of available options
        prompt: Description of what to select
        default_selections: List of default selections (by name)
        max_selections: Maximum number of selections allowed

    Returns:
        List of selected option names
    """
    if not options:
        print("No options available for selection.")
        return []

    print(f"\n{prompt}")
    print("Available options:")
    for i, option in enumerate(options, 1):
        print(f"  {i}. {option}")

    # Set up defaults
    default_indices = []
    if default_selections:
        for default in default_selections:
            if default in options:
                default_indices.append(options.index(default) + 1)

    if default_indices:
        print(f"\nDefault selections: {', '.join([str(i) for i in default_indices])}")

    print(f"\nEnter your selections:")
    print("- Enter numbers separated by commas (e.g., 1,3,5)")
    print("- Enter 'all' to select all options")
    print("- Press Enter to use defaults (if available)")
    if max_selections:
        print(f"- Maximum {max_selections} selections allowed")

    while True:
        try:
            user_input = input("Selection: ").strip()

            # Use defaults if empty input and defaults exist
            if not user_input and default_indices:
                selected_indices = default_indices
                break

            # Select all
            if user_input.lower() == 'all':
                if max_selections and len(options) > max_selections:
                    print(f"Cannot select all - maximum {max_selections} selections allowed.")
                    continue
                selected_indices = list(range(1, len(options) + 1))
                break

            # Parse comma-separated numbers
            if user_input:
                selected_indices = [int(x.strip()) for x in user_input.split(',')]

                # Validate selections
                if any(i < 1 or i > len(options) for i in selected_indices):
                    print(f"Invalid selection. Please enter numbers between 1 and {len(options)}")
                    continue

                if max_selections and len(selected_indices) > max_selections:
                    print(f"Too many selections. Maximum {max_selections} allowed.")
                    continue

                break
            else:
                print("Please enter a selection or press Enter for defaults.")

        except ValueError:
            print("Invalid input. Please enter numbers separated by commas.")

    # Convert indices to option names
    selected_options = [options[i-1] for i in selected_indices]
    print(f"Selected: {', '.join(selected_options)}")
    return selected_options


def console_select_single(options, prompt, default_selection=None):
    """
    Console-based single selection function.

    Args:
        options: List of available options
        prompt: Description of what to select
        default_selection: Default selection (by name)

    Returns:
        Selected option name
    """
    if not options:
        print("No options available for selection.")
        return None

    print(f"\n{prompt}")
    print("Available options:")
    for i, option in enumerate(options, 1):
        print(f"  {i}. {option}")

    # Set up default
    default_index = None
    if default_selection and default_selection in options:
        default_index = options.index(default_selection) + 1
        print(f"\nDefault selection: {default_index}")

    print(f"\nEnter your selection:")
    print("- Enter a number (1-{})".format(len(options)))
    print("- Press Enter to use default (if available)")

    while True:
        try:
            user_input = input("Selection: ").strip()

            # Use default if empty input and default exists
            if not user_input and default_index:
                selected_index = default_index
                break

            # Parse number
            if user_input:
                selected_index = int(user_input)

                # Validate selection
                if selected_index < 1 or selected_index > len(options):
                    print(f"Invalid selection. Please enter a number between 1 and {len(options)}")
                    continue

                break
            else:
                print("Please enter a selection or press Enter for default.")

        except ValueError:
            print("Invalid input. Please enter a number.")

    # Convert index to option name
    selected_option = options[selected_index-1]
    print(f"Selected: {selected_option}")
    return selected_option


# ## 1 – Interactive Well and Log Selection

# Create console-based interactive selection interface
print('Setting up interactive selection interface...')

# Get all available global well logs
available_logs = {}
for log in petrel.global_well_logs:
    if hasattr(log, 'petrel_name'):
        available_logs[log.petrel_name] = log
    elif isinstance(log, list):
        for sub_log in log:
            if hasattr(sub_log, 'petrel_name'):
                available_logs[sub_log.petrel_name] = sub_log

log_names = sorted(available_logs.keys())
print(f'Found {len(log_names)} global well logs')

# Get all wells
print('Scanning available wells...')
wells = [w for w in petrel.wells]
well_names = [w.petrel_name for w in wells]
print(f'Found {len(wells)} wells')

# Console-based well selection
default_wells = well_names[:min(5, len(well_names))]  # First 5 wells as default
selected_well_names = console_select_multiple(
    options=well_names,
    prompt="Select Wells for Analysis:",
    default_selections=default_wells,
    max_selections=None
)

# Console-based input logs selection
default_input_logs = ['GR', 'RHOB', 'NPHI', 'Vp'] if all(log in log_names for log in ['GR', 'RHOB', 'NPHI', 'Vp']) else log_names[:4] if len(log_names) >= 4 else log_names
selected_input_logs = console_select_multiple(
    options=log_names,
    prompt="Select Input Logs for ML Training:",
    default_selections=default_input_logs,
    max_selections=None
)

# Console-based target log selection
default_target_log = 'Vs' if 'Vs' in log_names else log_names[0] if log_names else None
selected_target_log = console_select_single(
    options=log_names,
    prompt="Select Target Log for Imputation:",
    default_selection=default_target_log
)

# Store selections for later use
log_selection_data = {
    'selected_wells': [w for w in wells if w.petrel_name in selected_well_names],
    'selected_well_names': selected_well_names,
    'selected_input_logs': selected_input_logs,
    'selected_target_log': selected_target_log,
    'available_logs': available_logs,
    'all_wells': wells
}

print(f'\n✓ Selection completed:')
print(f'  - Wells: {len(selected_well_names)} selected')
print(f'  - Input logs: {len(selected_input_logs)} selected')
print(f'  - Target log: {selected_target_log}')

def display_selection_summary():
    """Display a comprehensive summary of current selections."""
    print("\n" + "="*60)
    print("SELECTION SUMMARY")
    print("="*60)
    print(f"Project: {petrel.get_current_project_name()}")
    print(f"Selected Wells ({len(selected_well_names)}):")
    for i, well_name in enumerate(selected_well_names, 1):
        print(f"  {i}. {well_name}")

    print(f"\nSelected Input Logs ({len(selected_input_logs)}):")
    for i, log_name in enumerate(selected_input_logs, 1):
        print(f"  {i}. {log_name}")

    print(f"\nTarget Log for Imputation:")
    print(f"  {selected_target_log}")

    print(f"\nTotal Logs to Process: {len(LOG_NAMES)}")
    print("="*60)

# Display the summary
display_selection_summary()


# ## 2 – Configure wells & apply selection

# Get selected data from console selections
selected_wells = log_selection_data['selected_wells']
selected_well_names = log_selection_data['selected_well_names']
selected_input_logs = log_selection_data['selected_input_logs']
selected_target_log = log_selection_data['selected_target_log']

print(f'\n✓ Configuration applied:')
print(f'Selected {len(selected_wells)} wells: {[w.petrel_name for w in selected_wells]}')
print(f'Selected input logs: {selected_input_logs}')
print(f'Selected target log: {selected_target_log}')

# Combine all log names
LOG_NAMES = selected_input_logs + [selected_target_log]
print(f'All logs to process: {LOG_NAMES}')

# Helper function to find global well logs by name
def find_global_well_logs_by_names(names):
    found_logs = []
    for name in names:
        if name in available_logs:
            found_logs.append(available_logs[name])
        else:
            print(f'Warning: Log {name} not found in available logs')
    return found_logs

logs = find_global_well_logs_by_names(LOG_NAMES)
print(f'Using {len(logs)} global logs: {[g.petrel_name for g in logs]}')

# Validate that we have both wells and logs selected
if not selected_wells:
    print('ERROR: No wells selected! Please run the console selection again.')
    raise ValueError("No wells selected")
if not logs:
    print('ERROR: No valid logs found! Please check your log selection.')
    raise ValueError("No valid logs found")
if selected_wells and logs:
    print('✓ Ready to load data from selected wells and logs')

# Additional validation: Check if selected logs exist in selected wells
print('\n--- Validating log availability in selected wells ---')
for well in selected_wells[:3]:  # Check first 3 wells as sample
    well_log_names = [log.petrel_name for log in well.logs]
    print(f"\nWell: {well.petrel_name}")
    for log_name in LOG_NAMES:
        status = "✓" if log_name in well_log_names else "✗"
        print(f"  {status} {log_name}")

print(f'\n✓ Validation completed. Proceeding with {len(selected_wells)} wells and {len(logs)} logs.')


# ## 3 – Load log data from Petrel (Fixed with Error Handling)

# Ensure we have the required variables from previous sections
try:
    # Variables should be available from console selection
    if 'selected_wells' not in locals():
        print('ERROR: Console selection not completed. Please run section 1 first.')
        raise ValueError("Missing selection data")

    if 'logs' not in locals():
        # Use the console selections
        available_logs = log_selection_data['available_logs']

        def find_global_well_logs_by_names(names):
            found_logs = []
            for name in names:
                if name in available_logs:
                    found_logs.append(available_logs[name])
            return found_logs

        logs = find_global_well_logs_by_names(LOG_NAMES)
        
    print(f'Loading data from {len(selected_wells)} wells with {len(logs)} logs...')
    
    # Initialize empty DataFrame
    well_data = pd.DataFrame()
    
    # Load data from each selected well
    for i, w in enumerate(selected_wells):
        print(f'Processing well {i+1}/{len(selected_wells)}: {w.petrel_name}')
        try:
            df = w.logs_dataframe(logs)      # returns MD‑indexed DataFrame
            if not df.empty:
                df['WELL'] = w.petrel_name
                well_data = pd.concat([well_data, df], ignore_index=False)
                print(f'  ✓ Loaded {len(df)} samples')
            else:
                print(f'  ⚠ No data found for this well')
        except Exception as e:
            print(f'  ✗ Error loading data: {str(e)}')
    
    # Reset index to make MD a column
    if not well_data.empty:
        well_data.reset_index(drop=False, inplace=True)  # MD becomes column
        print(f'\n✓ Combined DataFrame shape: {well_data.shape}')
        print(f'Columns: {list(well_data.columns)}')
        print('\nFirst 5 rows of loaded data:')
        print(well_data.head().to_string())
    else:
        print('\n✗ No data loaded! Check your well and log selections.')
        
except Exception as e:
    print(f'Error in data loading: {str(e)}')
    print('Make sure you have run the previous cells to select wells and logs.')


# ## 4 – Basic cleaning (remove obvious spikes)

# Clean GR if it's in the selected logs
if 'GR' in well_data.columns:
    GR_MAX = 300
    well_data['GR'] = np.where(well_data['GR'] <= GR_MAX, well_data['GR'], np.nan)
    print(f'Cleaned GR values > {GR_MAX}')
    
# Clean NPHI if it's in the selected logs
if 'NPHI' in well_data.columns:
    well_data['NPHI'] = np.where(
        (well_data['NPHI'] >= 0) & (well_data['NPHI'] <= 1),
        well_data['NPHI'], np.nan
    )
    print('Cleaned NPHI values outside [0, 1] range')
    
# Clean RHOB if it's in the selected logs
if 'RHOB' in well_data.columns:
    well_data['RHOB'] = np.where(
        (well_data['RHOB'] >= 1.0) & (well_data['RHOB'] <= 3.5),
        well_data['RHOB'], np.nan
    )
    print('Cleaned RHOB values outside [1.0, 3.5] range')

print('\nData summary after cleaning:')
print(well_data.describe().T.to_string())


# ## 5 – Coverage per log

# Calculate coverage for selected logs only
log_columns = [col for col in LOG_NAMES if col in well_data.columns]
coverage = 1.0 - well_data[log_columns].isna().mean()
print('Data coverage per log:')
for log_name, cov in coverage.items():
    print(f'  {log_name}: {cov:.2%}')

# Visualize coverage
fig = px.bar(coverage, labels={'value':'Coverage'}, title='Relative data coverage for selected logs')
fig.show()


# ## 6 – Imputation utility with enhanced ML models

def impute_logs(df, depth_col, feature_cols, targets):
    """Return DataFrame with *_pred, *_imputed, *_error columns."""
    res = df.copy()
    # Configure ML models with user's preferred hyperparameters
    boosters = [
        ('EXTREME BOOST REGRESSOR', XGBRegressor(
            n_estimators=300,
            tree_method='gpu_hist',
            learning_rate=0.05,
            early_stopping_rounds=100,
            random_state=42
        )),
        ('LGBM REGRESSOR', LGBMRegressor(
            device='gpu',
            gpu_platform_id=1,
            gpu_device_id=0,
            n_estimators=300,
            random_state=42
        )),
        ('CATBOOST REGRESSOR', CatBoostRegressor(
            task_type='GPU',
            early_stopping_rounds=100,
            verbose=0,
            random_state=42
        ))
    ]
    feature_set = feature_cols + [depth_col]

    for tgt in targets:
        print(f'--- Imputing {tgt} ---')
        train = res[res[tgt].notna()][feature_set + [tgt]].copy()
        if train.empty:
            print('No training data, skipping.')
            continue
        X = train.drop(columns=[tgt]).apply(lambda c: c.fillna(c.mean()), axis=0)
        y = train[tgt]

        Xtr, Xval, ytr, yval = train_test_split(X, y, test_size=0.25, random_state=42)
        best_model, best_name, best_mae = None, None, float("inf")
        
        print(f'Training {len(boosters)} models on {len(Xtr)} samples...')
        for name, model in boosters:
            try:
                model.fit(Xtr, ytr)
                mae = mean_absolute_error(yval, model.predict(Xval))
                print(f'  {name}: MAE = {mae:.3f}')
                if mae < best_mae:
                    best_model, best_name, best_mae = model, name, mae
            except Exception as e:
                print(f'  {name}: Failed - {str(e)}')
                
        if best_model is None:
            print('All models failed! Skipping this target.')
            continue
            
        print(f'✓ Best model: {best_name} (MAE={best_mae:.3f})')

        X_full = res[feature_set].apply(lambda c: c.fillna(c.mean()), axis=0)
        preds = best_model.predict(X_full)
        res[f'{tgt}_pred'] = preds
        res[f'{tgt}_imputed'] = res[tgt].fillna(preds)
        res[f'{tgt}_error'] = np.abs(res[tgt] - preds) / res[tgt] * 100
        
        # Show imputation statistics
        missing_count = res[tgt].isna().sum()
        total_count = len(res)
        print(f'Imputed {missing_count}/{total_count} missing values ({missing_count/total_count:.1%})')
        
    return res


# ## 7 – Run imputation with selected logs

DEPTH_COL = 'MD'
FEATURES = selected_input_logs  # Use selected input logs
TARGET = selected_target_log    # Use selected target log

print(f'Running imputation for target: {TARGET}')
print(f'Using features: {FEATURES}')
print(f'Depth column: {DEPTH_COL}')

# Run the imputation
results = impute_logs(well_data, DEPTH_COL, FEATURES, targets=[TARGET])

print(f'\nImputation completed! Results shape: {results.shape}')
print(f'New columns added: {[col for col in results.columns if col.endswith("_pred") or col.endswith("_imputed") or col.endswith("_error")]}')

print('\nFirst 5 rows of results:')
print(results.head().to_string())


# ## 8 – (Option) Write imputed logs back to Petrel

def write_back_to_petrel(results_df, log_name_in_results, clone_from=None):
    """Clone an existing log (or first available log) and overwrite with imputed values."""
    # Ensure we have the selected wells
    if 'selected_wells' not in locals():
        print('ERROR: Console selection not completed. Please run section 1 first.')
        return

    if clone_from is None:
        clone_from = selected_target_log  # Use the selected target log as template
        
    print(f'Writing back {log_name_in_results} to {len(selected_wells)} wells...')
    success_count = 0
    
    for w in selected_wells:
        print(f'Updating {w.petrel_name}')
        well_df = results_df[results_df['WELL'] == w.petrel_name].set_index('MD')
        if well_df.empty:
            print(f'  ⚠ No data for well {w.petrel_name}, skipping')
            continue
            
        md = well_df.index.to_numpy()
        values = well_df[log_name_in_results].to_numpy()

        # Find a log to clone (or existing target)
        target_logs = [log for log in w.logs if log.petrel_name == log_name_in_results]
        if target_logs:
            log_obj = target_logs[0]
            print(f'  Using existing log {log_name_in_results}')
        else:
            # Clone template log
            template_logs = [log for log in w.logs if log.petrel_name == clone_from]
            if template_logs:
                template = template_logs[0]
                log_obj = template.clone(w, log_name_in_results)
                print(f'  Created new log {log_name_in_results} from template {clone_from}')
            else:
                print(f'  ✗ No template log {clone_from} found in well {w.petrel_name}, skipping')
                continue

        try:
            petrel_log_ref = petrel.well_logs[log_obj.path]
            petrel_log_ref.readonly = False
            petrel_log_ref.set_values(md, values)
            print(f'  ✓ Successfully updated {log_name_in_results} for {w.petrel_name}')
            success_count += 1
        except Exception as e:
            print(f'  ✗ Error updating {log_name_in_results} for {w.petrel_name}: {str(e)}')
    
    print(f'\nWrite-back completed: {success_count}/{len(selected_wells)} wells updated successfully.')

# Example usage - uncomment to execute
try:
    imputed_log_name = f'{selected_target_log}_imputed'
    print(f'To write back imputed values, run:')
    print(f'write_back_to_petrel(results, \'{imputed_log_name}\')')
    print('\nUncomment the line below to execute:')
    # write_back_to_petrel(results, imputed_log_name)
except:
    print('Run the console selection section first to enable write-back functionality.')


# ## 9 – Workflow Summary and Next Steps

print("\n" + "="*80)
print("MISSING LOG IMPUTATION WORKFLOW COMPLETED")
print("="*80)

try:
    print(f"✓ Project: {petrel.get_current_project_name()}")
    print(f"✓ Wells processed: {len(selected_wells)}")
    print(f"✓ Input logs used: {', '.join(selected_input_logs)}")
    print(f"✓ Target log imputed: {selected_target_log}")
    print(f"✓ Results shape: {results.shape}")

    # Calculate imputation statistics
    missing_before = well_data[selected_target_log].isna().sum()
    total_samples = len(well_data)
    imputation_rate = missing_before / total_samples * 100

    print(f"✓ Missing values imputed: {missing_before:,} ({imputation_rate:.1f}% of total data)")

    print("\nNEXT STEPS:")
    print("1. Review the imputation results above")
    print("2. Check the quality metrics and error statistics")
    print("3. Validate results against geological expectations")
    print("4. If satisfied, uncomment the write-back line to save results to Petrel")
    print("5. Consider running cross-validation for additional quality control")

    print(f"\nTO WRITE RESULTS TO PETREL:")
    print(f"write_back_to_petrel(results, '{selected_target_log}_imputed')")

except Exception as e:
    print(f"⚠ Workflow completed with some issues: {str(e)}")
    print("Please review the console output above for any errors.")

print("="*80)


